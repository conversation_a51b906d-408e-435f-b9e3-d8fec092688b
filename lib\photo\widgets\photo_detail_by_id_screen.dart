import 'package:easy_load_more/easy_load_more.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:portraitmode/app/config/colors.dart';
import 'package:portraitmode/app/config/config.dart';
import 'package:portraitmode/appbar/pm_app_bar.dart';
import 'package:portraitmode/appbar/pm_sliver_app_bar.dart';
import 'package:portraitmode/auth/utils/auth_util.dart';
import 'package:portraitmode/comment/dto/comment_data.dart';
import 'package:portraitmode/comment/http_responses/comment_list_response.dart';
import 'package:portraitmode/comment/providers/comment_activity_provider.dart';
import 'package:portraitmode/comment/providers/comment_list_provider.dart';
import 'package:portraitmode/comment/providers/comment_store_provider.dart';
import 'package:portraitmode/comment/services/comment_list_service.dart';
import 'package:portraitmode/comment/utils/delete_comment_util.dart';
import 'package:portraitmode/comment/utils/edit_comment_util.dart';
import 'package:portraitmode/comment/widgets/comment_form.dart';
import 'package:portraitmode/comment/widgets/comment_list_item.dart';
import 'package:portraitmode/dialogs/error_dialog.dart';
import 'package:portraitmode/hive/services/local_user_service.dart';
import 'package:portraitmode/photo/dto/photo_data.dart';
import 'package:portraitmode/photo/http_responses/photo_detail_response.dart';
import 'package:portraitmode/photo/providers/photo_store_provider.dart';
import 'package:portraitmode/photo/services/photo_service.dart';
import 'package:portraitmode/photo/utils/comment_util.dart';
import 'package:portraitmode/photo/widgets/photo_detail/photo_categories.dart';
import 'package:portraitmode/photo/widgets/photo_detail/photo_detail_description.dart';
import 'package:portraitmode/photo/widgets/photo_detail/photo_detail_frame.dart';
import 'package:portraitmode/photo/widgets/photo_detail/photo_metadata.dart';

class PhotoDetailByIdScreen extends ConsumerStatefulWidget {
  final int photoId;
  final bool isPhotoDetail;

  const PhotoDetailByIdScreen({
    super.key,
    required this.photoId,
    this.isPhotoDetail = true,
  });

  @override
  PhotoDetailByIdScreenState createState() => PhotoDetailByIdScreenState();
}

class PhotoDetailByIdScreenState extends ConsumerState<PhotoDetailByIdScreen> {
  final _scrollController = ScrollController();
  late final _photoDescriptionKey = GlobalKey();

  final PhotoService _photoService = PhotoService();
  final _commentListService = CommentListService();
  final _commentFieldController = TextEditingController();
  final _focusNode = FocusNode();

  final int _loadMorePerPage = LoadMoreConfig.photosPerPage;
  int _pageIndex = 0;
  bool _loadMoreEndReached = false;

  PhotoData? _photo;
  bool _isLoadingPhoto = true;

  final Map<int, GlobalKey> _activeCommentKeys = {};
  int? _activeCommentId;

  final double _sectionSpacing = 20.0;

  late final int _profileId;

  @override
  void initState() {
    super.initState();

    _profileId = LocalUserService.userId ?? 0;

    _fetchPhotoDetail();
  }

  @override
  void dispose() {
    _commentFieldController.dispose();
    _focusNode.dispose();
    _scrollController.dispose();
    _activeCommentKeys.clear();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: _isLoadingPhoto
          ? _buildLoadingWidget()
          : _photo == null
          ? _buildPhotoNotFoundWidget()
          : _buildPhotoDetailScreen(),
    );
  }

  Widget _buildLoadingWidget() {
    return Center(
      child: SizedBox(
        width: 24.0,
        height: 24.0,
        child: CircularProgressIndicator(
          color: context.colors.borderColor,
          strokeWidth: 3.0,
        ),
      ),
    );
  }

  Widget _buildPhotoNotFoundWidget() {
    return Column(
      children: [
        PmAppBar(
          titleText: 'Oops..',
          useLogo: false,
          automaticallyImplyLeading: true,
          actions: const [],
        ),
        const Expanded(
          child: Center(
            child: Text('Photo not found', style: TextStyle(fontSize: 16.0)),
          ),
        ),
      ],
    );
  }

  Widget _buildPhotoDetailScreen() {
    bool hasDescription = _photo!.description.isNotEmpty;

    final List<CommentData> commentList = ref.watch(
      commentListProvider(widget.photoId),
    );

    return Column(
      children: [
        Expanded(
          child: RefreshIndicator(
            onRefresh: _handleRefresh,
            child: CustomScrollView(
              controller: _scrollController,
              slivers: [
                PmSliverAppBar(
                  scrollController: _scrollController,
                  titleText: widget.isPhotoDetail
                      ? "Photo details"
                      : "Comments",
                  useLogo: false,
                  automaticallyImplyLeading: true,
                  actions: const [],
                ),
                if (widget.isPhotoDetail)
                  SliverToBoxAdapter(
                    child: Align(
                      alignment: Alignment.topCenter,
                      child: Container(
                        constraints: const BoxConstraints(maxWidth: 768.0),
                        child: PhotoDetailFrame(photo: _photo!),
                      ),
                    ),
                  ),
                if (hasDescription)
                  SliverToBoxAdapter(
                    child: PhotoDetailDescription(
                      key: _photoDescriptionKey,
                      photo: _photo!,
                      padding: EdgeInsets.only(
                        right: ScreenStyleConfig.horizontalPadding,
                        left: ScreenStyleConfig.horizontalPadding,
                        top: _sectionSpacing,
                      ),
                      contentToDividerGap: _sectionSpacing,
                    ),
                  ),
                if (widget.isPhotoDetail)
                  SliverToBoxAdapter(
                    child: Container(
                      constraints: const BoxConstraints(maxWidth: 768.0),
                      child: LayoutBuilder(
                        builder: (context, constraints) {
                          return PhotoMetadata(
                            photo: _photo!,
                            padding: EdgeInsets.only(
                              left: ScreenStyleConfig.horizontalPadding,
                              right: ScreenStyleConfig.horizontalPadding,
                              top: _sectionSpacing,
                            ),
                            contentToDividerGap: _sectionSpacing,
                          );
                        },
                      ),
                    ),
                  ),
                if (widget.isPhotoDetail && _photo!.categories.isNotEmpty)
                  SliverToBoxAdapter(
                    child: PhotoCategories(
                      categoryIds: _photo!.categories,
                      padding: EdgeInsets.only(
                        top: _sectionSpacing,
                        left: ScreenStyleConfig.horizontalPadding,
                        right: ScreenStyleConfig.horizontalPadding,
                      ),
                      contentToDividerGap: _sectionSpacing,
                    ),
                  ),
                if (widget.isPhotoDetail)
                  SliverToBoxAdapter(
                    child: Padding(
                      padding: EdgeInsets.only(
                        top: _sectionSpacing,
                        left: ScreenStyleConfig.horizontalPadding,
                        right: ScreenStyleConfig.horizontalPadding,
                      ),
                      child: const Text(
                        "Comments",
                        style: TextStyle(
                          fontSize: 15.0,
                          // fontWeight: FontWeight.w600,
                        ),
                      ),
                    ),
                  ),
                const SliverToBoxAdapter(
                  child: SizedBox(height: LayoutConfig.contentTopGap),
                ),
                EasyLoadMore(
                  isFinished: _loadMoreEndReached,
                  onLoadMore: _handleLoadMore,
                  loadingWidgetColor: context.colors.baseColorAlt,
                  runOnEmptyResult: true,
                  loadingStatusText: "",
                  finishedStatusText: commentList.isEmpty
                      ? 'No comments found'
                      : '',
                  statusTextColor: context.colors.primarySwatch[300],
                  child: _buildSliverListView(commentList),
                ),
                if (commentList.isEmpty)
                  const SliverToBoxAdapter(child: SizedBox(height: 30.0)),
              ],
            ),
          ),
        ),
        CommentForm(
          photoId: _photo!.id,
          fieldController: _commentFieldController,
          focusNode: _focusNode,
          photoDescriptionKey: _photoDescriptionKey,
          activeCommentKey: _activeCommentKeys[_activeCommentId],
        ),
      ],
    );
  }

  Widget _buildSliverListView(List<CommentData> commentList) {
    return SliverList(
      delegate: SliverChildBuilderDelegate((BuildContext context, int index) {
        double marginTop = index == 0 ? 0 : 12.0;
        final bool isOwnComment = commentList[index].authorId == _profileId;

        return Container(
          margin: EdgeInsets.only(top: marginTop),
          child: CommentListItem(
            commentFieldController: _commentFieldController,
            commentFieldFocusNode: _focusNode,
            comment: commentList[index],
            isOwnComment: isOwnComment,
            onEditCommentTap: isOwnComment
                ? () {
                    final int commentId = commentList[index].id;

                    setState(() {
                      _activeCommentId = commentId;
                      _activeCommentKeys[commentId] = GlobalKey();
                    });

                    handleEditCommentTap(
                      ref: ref,
                      comment: commentList[index],
                      commentFieldController: _commentFieldController,
                      commentFieldFocusNode: _focusNode,
                    );
                  }
                : null,
            onDeleteCommentTap: isOwnComment
                ? () async {
                    final activeCommentKey =
                        _activeCommentKeys[commentList[index].id];

                    if (activeCommentKey == null) {
                      return;
                    }

                    await DeleteCommentUtil(
                      context: context,
                      ref: ref,
                      photoId: widget.photoId,
                      commentToDelete: commentList[index],
                      targetKey: activeCommentKey,
                    ).handleDeleteEvent();
                  }
                : null,
          ),
        );
      }, childCount: commentList.length),
    );
  }

  Future<void> _fetchPhotoDetail() async {
    // log('Fetching photo with id: ${widget.photoId.toString()}');
    PhotoDetailResponse response = await _photoService.find(widget.photoId);

    if (!response.success) {
      if (mounted) {
        setState(() {
          _isLoadingPhoto = false;
        });
      }
    }

    if (mounted) {
      setState(() {
        _photo = response.data;
        _isLoadingPhoto = false;
      });
    }
  }

  Future<void> _handleRefresh() async {
    ref.read(commentActivityProvider.notifier).reset();
    // clearCommentProviders();

    _pageIndex = 0;
    _loadMoreEndReached = false;

    CommentListResponse response = await _commentListService.fetch(
      isRefreshAction: true,
      postId: _photo!.id,
      limit: _loadMorePerPage,
      offset: _pageIndex * _loadMorePerPage,
    );

    _handleCommentListResponse(response, true, false);
  }

  Future<bool> _handleLoadMore() async {
    CommentListResponse response = await _commentListService.fetch(
      postId: _photo!.id,
      limit: _loadMorePerPage,
      offset: _pageIndex * _loadMorePerPage,
    );

    final isFirstLoad = _pageIndex == 0;

    _handleCommentListResponse(response, false, isFirstLoad);

    return response.success;
  }

  void _handleCommentListResponse(
    CommentListResponse response,
    bool isRefresh,
    bool isFirstLoad,
  ) {
    if (!response.success) {
      if (authUtil.errorCodeRequiresLogin(response.errorCode)) {
        showSessionEndedDialog(context, ref);
      }

      return;
    }

    if (response.data.isEmpty) {
      // Update the related photo's totalComments
      ref
          .read(photoStoreProvider.notifier)
          .setTotalComments(
            widget.photoId,
            ref.read(commentListLengthProvider(widget.photoId)),
          );

      if (mounted) {
        setState(() {
          _loadMoreEndReached = true;
        });
      }

      return;
    }

    ref.read(commentStoreProvider.notifier).updateItems(response.data);

    _pageIndex++;

    // Sort the array before saving it (last comes first).
    response.data.sort((a, b) => b.id.compareTo(a.id));

    if (isRefresh) {
      ref
          .read(commentListManagerProvider.notifier)
          .replaceAll(widget.photoId, commentListToIdList(response.data));
    } else {
      if (isFirstLoad) {
        ref
            .read(commentListManagerProvider.notifier)
            .replaceAll(widget.photoId, commentListToIdList(response.data));
      } else {
        ref
            .read(commentListManagerProvider.notifier)
            .addAll(widget.photoId, commentListToIdList(response.data));
      }
    }

    if (response.data.length < _loadMorePerPage) {
      // Update related photo's totalComments
      ref
          .read(photoStoreProvider.notifier)
          .setTotalComments(
            widget.photoId,
            ref.read(commentListLengthProvider(widget.photoId)),
          );

      if (mounted) {
        setState(() {
          _loadMoreEndReached = true;
        });
      }
    }
  }
}

import 'dart:math' as math;

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:portraitmode/app/config/colors.dart';
import 'package:portraitmode/app/config/config.dart';
import 'package:portraitmode/app/providers/app_state_provider.dart';
import 'package:portraitmode/app/utils/scroll_util.dart';
import 'package:portraitmode/artist/dto/artist_data.dart';
import 'package:portraitmode/artist/http_responses/artist_list_response.dart';
import 'package:portraitmode/artist/providers/following_artists_provider.dart';
import 'package:portraitmode/artist/services/artist_list_service.dart';
import 'package:portraitmode/artist/widgets/artist_list_slider.dart';
import 'package:portraitmode/auth/utils/auth_util.dart';
import 'package:portraitmode/common/utils/common_refresh_util.dart';
import 'package:portraitmode/dialogs/error_dialog.dart';
import 'package:portraitmode/hive/services/local_user_service.dart';
import 'package:portraitmode/home/<USER>/empty_following_notice.dart';
import 'package:portraitmode/photo/dto/photo_data.dart';
import 'package:portraitmode/photo/http_responses/photo_list_response.dart';
import 'package:portraitmode/photo/providers/following_photos_provider.dart';
import 'package:portraitmode/photo/providers/photo_store_provider.dart';
import 'package:portraitmode/photo/services/photo_list_service.dart';
import 'package:portraitmode/photo/utils/photo_util.dart';
import 'package:portraitmode/photo/widgets/photo_list_item.dart';
import 'package:portraitmode/profile/providers/profile_provider.dart';

final globalFollowingPhotosTabContentKey =
    GlobalKey<FollowingPhotosTabContentState>();

class FollowingPhotosTabContent extends ConsumerStatefulWidget {
  const FollowingPhotosTabContent({super.key});

  @override
  FollowingPhotosTabContentState createState() =>
      FollowingPhotosTabContentState();
}

class FollowingPhotosTabContentState
    extends ConsumerState<FollowingPhotosTabContent>
    with AutomaticKeepAliveClientMixin<FollowingPhotosTabContent> {
  final _scrollController = ScrollController();
  final refreshIndicatorKey = GlobalKey<RefreshIndicatorState>();
  late final int _profileId;

  final int _loadMorePerPage = LoadMoreConfig.photosPerPage;
  int _loadMoreLastId = -1;
  int _currentPageNumber = 0;
  bool _loadMoreEndReached = false;
  bool _isLoadingMore = false;
  final List<int> _currentlyViewedPhotoIds = [];
  List<ArtistData> _recentlyUploadedArtistList = [];

  bool _dataFetched = false;

  final PhotoListService _photoListService = PhotoListService();
  final ArtistListService _artistListService = ArtistListService();

  double? _loadMoreThreshold;

  @override
  bool get wantKeepAlive => true;

  @override
  void initState() {
    super.initState();

    _profileId = LocalUserService.userId ?? 0;
    _scrollController.addListener(_onScroll);

    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (_canLoadMore()) _triggerLoadMore();
    });
  }

  @override
  void dispose() {
    _scrollController.removeListener(_onScroll);
    _scrollController.dispose();
    super.dispose();
  }

  void _onScroll() {
    if (_canLoadMore()) _triggerLoadMore();
  }

  double _getLoadMoreThreshold() {
    if (_loadMoreThreshold == null) {
      final screenHeight = MediaQuery.of(context).size.height;
      final dynamicThreshold = screenHeight * LoadMoreConfig.tresholdByScreen;

      // Add bounds by min and max.
      _loadMoreThreshold = math.max(
        math.min(dynamicThreshold, LoadMoreConfig.maxTreshold),
        LoadMoreConfig.minTreshold,
      );
    }
    return _loadMoreThreshold!;
  }

  bool _canLoadMore() {
    return _scrollController.position.pixels >=
            _scrollController.position.maxScrollExtent -
                _getLoadMoreThreshold() &&
        !_isLoadingMore &&
        !_loadMoreEndReached;
  }

  void _triggerLoadMore() async {
    if (_isLoadingMore || _loadMoreEndReached) return;

    setState(() {
      _isLoadingMore = true;
    });

    await _handleLoadMore();

    if (mounted) {
      setState(() {
        _isLoadingMore = false;
      });
    }
  }

  double? _cacheExtent;

  @override
  Widget build(BuildContext context) {
    super.build(context);

    if (_cacheExtent == null) {
      final double screenHeight = MediaQuery.sizeOf(context).height;
      _cacheExtent = screenHeight * 2.5;
    }

    int totalFollowing = ref.watch(
      profileProvider.select((value) => value.totalFollowing),
    );

    // log('totalFollowing: $totalFollowing');

    if (_dataFetched && totalFollowing < 1) {
      return const EmptyFollowingNotice();
    }

    bool behaveNewlyOpened = ref.watch(
      appStateProvider.select((data) => data.behaveNewlyOpened),
    );

    if (behaveNewlyOpened) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        scrollToTop();

        if (mounted) {
          // Reset the behaveNewlyOpened flag after handling it.
          ref.read(appStateProvider.notifier).setBehaveNewlyOpened(false);
        }
      });
    }

    List<PhotoData> photoList = ref.watch(followingPhotosProvider);

    return _buildScreen(photoList);
  }

  Widget _buildScreen(List<PhotoData> photoList) {
    return Center(
      child: Container(
        color: context.colors.scaffoldColor,
        constraints: const BoxConstraints(maxWidth: 768.0),
        child: RefreshIndicator(
          key: refreshIndicatorKey,
          onRefresh: _handleRefresh,
          child: ListView.builder(
            controller: _scrollController,
            cacheExtent: _cacheExtent,
            itemCount:
                photoList.length +
                (_isLoadingMore && !_loadMoreEndReached ? 1 : 0),
            itemBuilder: (BuildContext context, int index) {
              // Handle loading indicator as the last item
              if (index == photoList.length) {
                return Container(
                  padding: const EdgeInsets.all(16.0),
                  alignment: Alignment.center,
                  child: CircularProgressIndicator(
                    color: context.colors.baseColorAlt,
                  ),
                );
              }

              double marginTop = index == 0 ? LayoutConfig.contentTopGap : 12.0;
              bool isOwnProfile = photoList[index].authorId == _profileId;

              return Container(
                key: ValueKey(photoList[index].id),
                margin: EdgeInsets.only(top: marginTop),
                child: (index == 18
                    ? Column(
                        children: [
                          Padding(
                            padding: const EdgeInsets.only(bottom: 20.0),
                            child: ArtistListSlider(
                              artistList: _recentlyUploadedArtistList,
                            ),
                          ),
                          _buildPhotoListItem(
                            index,
                            photoList[index],
                            isOwnProfile,
                          ),
                        ],
                      )
                    : _buildPhotoListItem(
                        index,
                        photoList[index],
                        isOwnProfile,
                      )),
              );
            },
          ),
        ),
      ),
    );
  }

  Widget _buildPhotoListItem(int index, PhotoData photo, bool isOwnProfile) {
    return PhotoListItem(
      index: index,
      photo: photo,
      isOwnProfile: isOwnProfile,
      screenName: 'explore_screen',
    );
  }

  void scrollToTop() async {
    // log('FollowingPhotosTabContent: scrolling to top ');
    scrollListToTop(_scrollController, refreshIndicatorKey);
  }

  Future<void> _handleRefresh() async {
    _loadMoreLastId = -1;
    _loadMoreEndReached = false;
    _isLoadingMore = false;

    List<dynamic> reponses = await Future.wait([
      _photoListService.fetchFromFollowedArtists(
        limit: _loadMorePerPage,
        lastId: _loadMoreLastId,
      ),
      CommonRefreshUtil().fetch(ref, _profileId),
    ]);

    final PhotoListResponse photoListResponse = reponses[0];

    _handlePhotosResponse(photoListResponse, true, false);
  }

  Future<bool> _handleLoadMore() async {
    List<dynamic> responses = [];
    late PhotoListResponse photoListResponse;
    late ArtistListResponse artistListResponse;
    bool shouldUpdateArtistList = false;
    final List<int> viewedPhotoIds = _currentPageNumber > 0
        ? _currentlyViewedPhotoIds
        : [];

    if (_currentPageNumber == 1) {
      shouldUpdateArtistList = true;

      responses = await Future.wait([
        _photoListService.fetchFromFollowedArtists(
          limit: _loadMorePerPage,
          lastId: _loadMoreLastId,
          viewedPhotoIds: viewedPhotoIds,
        ),
        _artistListService.fetch(limit: 12),
      ]);

      photoListResponse = responses[0];
      artistListResponse = responses[1];
    } else {
      photoListResponse = await _photoListService.fetchFromFollowedArtists(
        limit: _loadMorePerPage,
        lastId: _loadMoreLastId,
        viewedPhotoIds: viewedPhotoIds,
      );
    }

    final isFirstLoad = _loadMoreLastId == -1;

    _handlePhotosResponse(photoListResponse, false, isFirstLoad);

    if (shouldUpdateArtistList &&
        mounted &&
        artistListResponse.success &&
        artistListResponse.data.isNotEmpty) {
      setState(() {
        _recentlyUploadedArtistList = artistListResponse.data;
      });
    }

    return photoListResponse.success;
  }

  void _handlePhotosResponse(
    PhotoListResponse response,
    bool isRefresh,
    bool isFirstLoad,
  ) {
    if (!response.success) {
      _currentlyViewedPhotoIds.clear();

      if (authUtil.errorCodeRequiresLogin(response.errorCode)) {
        showSessionEndedDialog(context, ref);
      } else {
        showErrorDialog(context, ref, message: response.message);
      }

      return;
    }

    if (response.data.isEmpty) {
      _currentlyViewedPhotoIds.clear();

      if (isRefresh) {
        ref.read(followingArtistIdsProvider.notifier).replaceAll([]);
      }

      if (mounted) {
        setState(() {
          _loadMoreEndReached = true;
          _dataFetched = true;
          _isLoadingMore = false;
        });
      }

      return;
    }

    ref
        .read(photoStoreProvider.notifier)
        .updateItems(response.data, addIfNotExists: true);

    _loadMoreLastId = response.data.last.id;

    // Sort the array before saving it (last comes first).
    response.data.sort((a, b) => b.id.compareTo(a.id));

    // Update the list of currently viewed photo ids.
    _currentlyViewedPhotoIds.clear();
    _currentlyViewedPhotoIds.addAll(
      response.data.map((photo) => photo.id).toList(),
    );

    if (isRefresh) {
      ref
          .read(followingPhotoIdsProvider.notifier)
          .replaceAll(photoListToIdList(response.data));
    } else {
      if (isFirstLoad) {
        ref
            .read(followingPhotoIdsProvider.notifier)
            .replaceAll(photoListToIdList(response.data));
      } else {
        ref
            .read(followingPhotoIdsProvider.notifier)
            .addItems(photoListToIdList(response.data));
      }
    }

    if (mounted) {
      setState(() {
        if (isRefresh) {
          _currentPageNumber = 1;
          _isLoadingMore = false;
        } else {
          _currentPageNumber = isFirstLoad ? 1 : _currentPageNumber + 1;
        }

        _dataFetched = true;
      });
    }
  }
}
